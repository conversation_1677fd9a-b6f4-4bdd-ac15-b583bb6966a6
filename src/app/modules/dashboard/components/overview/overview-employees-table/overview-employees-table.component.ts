import { Component, OnInit } from '@angular/core';
import { Ng<PERSON><PERSON>, NgF<PERSON>, NgIf } from '@angular/common';
import { OverviewEmployeesTableItemComponent } from '../overview-employees-table-item/overview-employees-table-item.component';
import { EmployeeService } from 'src/app/core/services/employee/employee.service';
import { EmployeeCreateModalComponent } from '../../../pages/employees/components/employee-create-modal/employee-create-modal.component';
import { Router } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/core/store/app.state';
import { selectCurrentCompany } from 'src/app/core/store/company/company.selector';
import { StoreCompany } from 'src/app/core/models/company.model';
import {
  EmployeeWithRelations,
  PaginatedResult,
  EmployeeQueryOptions,
} from 'src/app/core/models';

@Component({
  selector: '[overview-employees-table]',
  templateUrl: './overview-employees-table.component.html',
  imports: [
    Ng<PERSON><PERSON>,
    NgIf,
    EmployeeCreateModalComponent,
    OverviewEmployeesTableItemComponent,
    NgClass,
  ],
})
export class OverviewEmployeesTableComponent implements OnInit {
  employees: EmployeeWithRelations[] = [];
  showCreateModal = false;
  selectedEmployee: EmployeeWithRelations | null = null;
  isLoading = true;
  company!: StoreCompany;

  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;
  pages: number[] = [];

  constructor(
    private employeeService: EmployeeService,
    private router: Router,
    private store: Store<AppState>
  ) {}

  ngOnInit(): void {
    this.store.pipe(select(selectCurrentCompany)).subscribe((company) => {
      this.company = company;
    });

    this.getEmployees();
  }

  getEmployees(): void {
    this.isLoading = true;

    const options: EmployeeQueryOptions = {
      pagination: {
        page: this.currentPage,
        limit: this.pageSize,
      },
    };

    this.employeeService
      .getEmployeesByCompany(this.company.id!, options)
      .subscribe({
        next: (data: PaginatedResult<EmployeeWithRelations>) => {
          this.employees = data.items;
          this.totalItems = data.total;
          this.totalPages = data.totalPages;
          this.currentPage = data.limit;
          this.updatePaginationPages();
          this.isLoading = false;
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error fetching employees:', error);
        },
      });
  }

  // Pagination methods
  updatePaginationPages(): void {
    const maxVisiblePages = 5;
    const pages: number[] = [];

    let startPage = Math.max(
      1,
      this.currentPage - Math.floor(maxVisiblePages / 2)
    );
    let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

    // Adjust start page if we're near the end
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    this.pages = pages;
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.selectedEmployee = null; // Clear selection when changing pages
      this.getEmployees();
    }
  }

  goToFirstPage(): void {
    this.goToPage(1);
  }

  goToLastPage(): void {
    this.goToPage(this.totalPages);
  }

  goToPreviousPage(): void {
    this.goToPage(this.currentPage - 1);
  }

  goToNextPage(): void {
    this.goToPage(this.currentPage + 1);
  }

  onPageSizeChange(newSize: number): void {
    this.pageSize = newSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.selectedEmployee = null;
    this.getEmployees();
  }

  // Helper methods for pagination display
  get startIndex(): number {
    return (this.currentPage - 1) * this.pageSize + 1;
  }

  get endIndex(): number {
    return Math.min(this.currentPage * this.pageSize, this.totalItems);
  }

  get hasPreviousPage(): boolean {
    return this.currentPage > 1;
  }

  get hasNextPage(): boolean {
    return this.currentPage < this.totalPages;
  }

  // Existing methods remain unchanged
  openCreateModal(): void {
    this.showCreateModal = true;
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
  }

  addEmployee(employeeData: any): void {
    this.employeeService
      .createEmployee(this.company.id!, employeeData)
      .subscribe({
        next: (response) => {
          if (response) {
            // If we're on the last page and it might be full, stay on current page
            // Otherwise, go to first page to see the new employee
            if (
              this.currentPage === this.totalPages &&
              this.employees.length < this.pageSize
            ) {
              this.getEmployees(); // Stay on current page
            } else {
              this.currentPage = 1; // Go to first page
              this.getEmployees();
            }
            this.closeCreateModal();
          }
        },
        error: (error) => {
          console.error("Erreur lors de l'ajout de l'employé:", error);
        },
      });
  }

  selectEmployee(employee: EmployeeWithRelations): void {
    this.selectedEmployee =
      this.selectedEmployee === employee ? null : employee;
  }

  clearSelection(): void {
    this.selectedEmployee = null;
  }

  editEmployee(employee: EmployeeWithRelations): void {
    if (employee && employee.id) {
      this.router.navigate(['/dashboard/employees', employee.id, 'profile']);
    }
  }

  deleteEmployee(employee: EmployeeWithRelations): void {
    if (
      confirm(
        `Voulez-vous vraiment supprimer ${employee.user.profile.firstName} ${employee.user.profile.lastName} ?`
      )
    ) {
      this.employeeService
        .deleteEmployee(this.company.id!, employee.id) // Assuming correct delete method
        .subscribe({
          next: () => {
            // If we're on a page that would become empty after deletion, go to previous page
            if (this.employees.length === 1 && this.currentPage > 1) {
              this.currentPage--;
            }
            this.getEmployees();
            this.clearSelection();
          },
          error: (error) => {
            console.error('Error deleting employee:', error);
          },
        });
    }
  }
}
